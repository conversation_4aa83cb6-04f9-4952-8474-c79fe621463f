#!/usr/bin/env python3
"""
AI聊天系统 - 支持tool_use调用MCP保存代码
正确的架构：用户 → AI聊天 → LLM返回tool_use → 解析并调用MCP
"""

import os
import json
import asyncio
import argparse
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
import openai
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 加载环境变量
load_dotenv('.evn')

class AIChat:
    def __init__(self):
        self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        self.openrouter_api_url = os.getenv('OPENROUTER_API_URL')
        self.model = os.getenv('OpenRouter_models', 'google/gemini-2.5-flash')
        
        # 创建输出目录
        self.output_dir = "generated_code"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 配置OpenAI客户端
        self.client = openai.OpenAI(
            api_key=self.openrouter_api_key,
            base_url=self.openrouter_api_url,
            timeout=60.0
        )
        
        # MCP客户端
        self.mcp_session = None
        self.mcp_context = None
        
        # 对话历史
        self.conversation_history = []
    
    async def setup_mcp_client(self):
        """设置MCP客户端连接"""
        try:
            server_params = StdioServerParameters(
                command=os.getenv('MCP_SERVER_COMMAND', 'python'),
                args=os.getenv('MCP_SERVER_ARGS', 'file_server.py').split()
            )
            
            self.mcp_context = stdio_client(server_params)
            self.read_stream, self.write_stream = await self.mcp_context.__aenter__()
            self.mcp_session = ClientSession(self.read_stream, self.write_stream)
            await self.mcp_session.initialize()
            print("✅ MCP服务器连接成功")
            return True
        except Exception as e:
            print(f"⚠️  MCP服务器连接失败: {e}")
            self.mcp_session = None
            return False
    
    async def cleanup_mcp_client(self):
        """清理MCP连接"""
        try:
            if self.mcp_context:
                await self.mcp_context.__aexit__(None, None, None)
                print("✅ MCP连接已关闭")
        except Exception as e:
            print(f"⚠️  关闭MCP连接时出错: {e}")
        finally:
            self.mcp_session = None
            self.mcp_context = None
    
    def get_system_prompt(self):
        """获取系统提示，包含tool定义"""
        return """你是一个专业的编程助手。当用户请求生成代码时，你需要：

1. 首先生成高质量的代码
2. 然后使用TOOL_CALL格式来保存代码文件

当需要保存代码时，请在回复的最后添加以下格式：

TOOL_CALL:save_code
FILENAME:文件名.扩展名
DESCRIPTION:代码描述
CONTENT_START
[这里是完整的代码内容]
CONTENT_END

示例：
用户：生成一个Python函数计算阶乘
你的回复：我来为您生成一个计算阶乘的Python函数...

TOOL_CALL:save_code
FILENAME:factorial.py
DESCRIPTION:计算阶乘的Python函数
CONTENT_START
def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)
CONTENT_END

请确保：
- 代码完整且可运行
- 包含适当的注释
- 文件名有正确的扩展名
- 描述简洁明了"""
    
    async def chat_with_ai(self, user_message: str) -> str:
        """与AI聊天并处理tool_use"""
        # 添加用户消息到历史
        self.conversation_history.append({"role": "user", "content": user_message})
        
        try:
            print(f"🤖 正在调用 {self.model}...")
            
            # 调用LLM
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.get_system_prompt()},
                    *self.conversation_history
                ],
                temperature=0.7,
                max_tokens=3000
            )
            
            ai_response = response.choices[0].message.content.strip()
            
            # 添加AI响应到历史
            self.conversation_history.append({"role": "assistant", "content": ai_response})
            
            # 检查是否包含工具调用
            if "TOOL_CALL:" in ai_response:
                await self.handle_tool_call(ai_response)
            
            return ai_response
            
        except Exception as e:
            error_msg = f"❌ AI调用失败: {e}"
            print(error_msg)
            return error_msg
    
    async def handle_tool_call(self, ai_response: str):
        """处理AI响应中的工具调用"""
        try:
            import re

            # 查找TOOL_CALL模式
            pattern = r'TOOL_CALL:(\w+)\s*\nFILENAME:([^\n]+)\s*\nDESCRIPTION:([^\n]+)\s*\nCONTENT_START\s*\n(.*?)\s*\nCONTENT_END'
            matches = re.findall(pattern, ai_response, re.DOTALL)

            for match in matches:
                tool_name, filename, description, content = match

                if tool_name == "save_code":
                    await self.save_code_tool({
                        "filename": filename.strip(),
                        "content": content.strip(),
                        "description": description.strip()
                    })

        except Exception as e:
            print(f"⚠️  处理工具调用时出错: {e}")
    
    async def execute_tool(self, tool_call: Dict[str, Any]):
        """执行工具调用"""
        tool_name = tool_call.get("name")
        parameters = tool_call.get("parameters", {})
        
        if tool_name == "save_code":
            await self.save_code_tool(parameters)
        else:
            print(f"⚠️  未知工具: {tool_name}")
    
    async def save_code_tool(self, parameters: Dict[str, Any]):
        """保存代码工具"""
        filename = parameters.get("filename", "generated_code.txt")
        content = parameters.get("content", "")
        description = parameters.get("description", "生成的代码")
        
        print(f"💾 保存代码: {filename}")
        print(f"📝 描述: {description}")
        
        # 确保文件名包含时间戳避免冲突
        import time
        timestamp = int(time.time())
        name, ext = os.path.splitext(filename)
        filename_with_timestamp = f"{name}_{timestamp}{ext}"
        
        # 尝试通过MCP保存
        if self.mcp_session:
            try:
                filepath = os.path.join(self.output_dir, filename_with_timestamp)
                result = await self.mcp_session.call_tool(
                    "write_file",
                    {
                        "filename": filepath,
                        "content": content
                    }
                )
                print(f"✅ 通过MCP保存成功: {filepath}")
                return
            except Exception as e:
                print(f"⚠️  MCP保存失败: {e}")
        
        # 本地保存作为备选
        try:
            filepath = os.path.join(self.output_dir, filename_with_timestamp)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 本地保存成功: {os.path.abspath(filepath)}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def display_response(self, response: str):
        """显示AI响应，过滤掉工具调用部分"""
        # 移除TOOL_CALL部分
        import re
        clean_response = re.sub(r'\nTOOL_CALL:.*?CONTENT_END', '', response, flags=re.DOTALL)
        clean_response = clean_response.strip()

        if clean_response:
            print(f"\n🤖 AI回复:")
            print("-" * 50)
            print(clean_response)
            print("-" * 50)
    
    async def interactive_chat(self):
        """交互式聊天"""
        print("🚀 AI编程助手启动")
        print("💡 您可以要求我生成各种代码，我会自动保存到文件")
        print("📁 文件保存目录:", os.path.abspath(self.output_dir))
        print("🔧 输入 'quit' 或 'exit' 退出")
        print("=" * 60)
        
        while True:
            try:
                user_input = input("\n👤 您: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    break
                
                if not user_input:
                    continue
                
                # 与AI聊天
                response = await self.chat_with_ai(user_input)
                
                # 显示响应
                self.display_response(response)
                
            except KeyboardInterrupt:
                print("\n\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 出错: {e}")

async def main():
    parser = argparse.ArgumentParser(description='AI聊天系统 - 支持MCP工具调用')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')
    parser.add_argument('message', nargs='?', help='单次对话消息')
    
    args = parser.parse_args()
    
    chat = AIChat()
    
    try:
        # 尝试连接MCP服务器
        await chat.setup_mcp_client()
        
        if args.interactive:
            await chat.interactive_chat()
        elif args.message:
            response = await chat.chat_with_ai(args.message)
            chat.display_response(response)
        else:
            print("请使用 -i 进入交互模式或提供消息")
            print("示例: python ai_chat_with_mcp.py -i")
            print("示例: python ai_chat_with_mcp.py '生成一个Python计算器'")
    
    finally:
        await chat.cleanup_mcp_client()

if __name__ == "__main__":
    asyncio.run(main())
