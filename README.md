# AI聊天系统 - 支持MCP工具调用生成代码

这是一个正确架构的AI聊天系统，通过MCP（Model Context Protocol）协议调用工具来生成和保存代码文件。

## 🏗️ 系统架构

```
用户输入 → AI聊天 → LLM返回(包含tool_use) → 解析tool_use → 调用MCP工具 → 保存代码文件
```

**正确的流程：**
1. 用户向AI提出代码生成需求
2. LLM分析需求并返回包含`tool_use`的响应
3. 系统解析`tool_use`调用
4. 通过MCP协议调用文件保存工具
5. 代码文件被保存到指定目录

## 🌟 功能特性

- 🤖 智能AI聊天，理解代码生成需求
- 🔧 基于tool_use的MCP工具调用
- 🌐 使用OpenRouter API调用大语言模型
- 📁 通过MCP协议进行文件操作
- 💾 支持本地文件保存作为备选方案
- 💬 保持对话上下文和连续性
- 📂 自动创建 `generated_code` 输出目录
- ✅ 完善的错误处理和状态提示
- 🎯 支持多种编程语言代码生成

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

确保 `.evn` 文件包含以下配置：

```env
# OpenRouter API配置
OPENROUTER_API_KEY=your-api-key-here
OPENROUTER_API_URL=https://api.openrouter.ai/api/v1
OpenRouter_models=google/gemini-2.5-pro

# MCP服务器配置
MCP_SERVER_COMMAND=python
MCP_SERVER_ARGS=file_server.py
```

## 🚀 使用方法

### 快速开始

```bash
# 一键启动（推荐）
python start_ai_chat.py

# 或者手动启动交互模式
python ai_chat_with_mcp.py -i

# 单次对话
python ai_chat_with_mcp.py "生成一个Python计算器程序"
```

### 交互式聊天

启动后，您可以像与AI助手聊天一样提出代码生成需求：

```
👤 您: 请生成一个Python函数，计算两个数的最大公约数
🤖 AI回复: 我来为您生成一个计算最大公约数的Python函数...
💾 保存代码: gcd_function_1234567890.py
✅ 通过MCP保存成功: generated_code/gcd_function_1234567890.py

👤 您: 创建一个HTML登录页面
🤖 AI回复: 我来为您创建一个美观的HTML登录页面...
💾 保存代码: login_page_1234567891.html
✅ 通过MCP保存成功: generated_code/login_page_1234567891.html
```

## 支持的代码类型

- **Python** (.py) - 关键词：python, py, .py
- **HTML** (.html) - 关键词：html, web, 网页, 页面, .html
- **CSS** (.css) - 关键词：css, 样式, .css
- **JavaScript** (.js) - 关键词：javascript, js, .js

## 程序工作流程

1. **输入解析**：判断用户输入是否为代码生成请求
2. **类型检测**：自动识别要生成的代码类型
3. **LLM调用**：使用大语言模型生成代码
4. **代码展示**：在终端中显示生成的代码
5. **文件保存**：通过MCP服务器或本地方式保存文件

## 示例请求

### Python代码生成
```
"生成一个Python爬虫程序"
"写一个数据分析脚本"
"创建一个Flask Web应用"
```

### HTML代码生成
```
"创建一个响应式网页"
"生成一个产品展示页面"
"写一个HTML表单"
```

### CSS代码生成
```
"写一个现代化的按钮样式"
"创建一个网格布局CSS"
"生成一个动画效果"
```

## 📁 文件结构

```
.
├── ai_chat_with_mcp.py        # 主程序 - AI聊天系统
├── file_server.py             # MCP文件服务器
├── start_ai_chat.py           # 一键启动脚本
├── test_new_architecture.py   # 新架构测试
├── requirements.txt           # Python依赖
├── .evn                      # 环境配置
├── generated_code/           # 自动创建的输出目录
└── README.md                 # 说明文档

# 旧版本文件（保留用于参考）
├── code_generator.py         # 旧版代码生成器
├── start.py                  # 旧版启动脚本
└── demo.py                   # 演示脚本
```

## 注意事项

1. 确保OpenRouter API密钥有效
2. MCP服务器需要单独启动（或使用 `start.py` 自动启动）
3. 生成的文件会保存在 `generated_code/` 目录中
4. 如果MCP连接失败，程序会自动使用本地文件保存
5. 程序会自动创建输出目录，无需手动创建

## 测试功能

### 基本功能测试
```bash
python simple_test.py
```

### 完整演示
```bash
python demo.py
```

### 文件输出测试
```bash
python test_file_output.py
```

这些测试将验证：
- ✅ 本地文件保存功能
- ✅ 输出目录自动创建
- ✅ 代码类型检测
- ✅ 请求识别功能
- ⚠️ MCP服务器连接（可选）

## 已验证功能

✅ **文件输出正常** - 所有生成的代码文件都会保存到 `generated_code/` 目录
✅ **目录自动创建** - 程序会自动创建输出目录
✅ **多种代码类型** - 支持Python、HTML、CSS、JavaScript
✅ **智能识别** - 能够正确识别代码生成请求和代码类型
✅ **错误处理** - 完善的错误处理和状态提示
✅ **本地保存备选** - 即使MCP连接失败也能正常保存文件

## 扩展功能

可以根据需要扩展以下功能：

- 支持更多编程语言
- 添加代码质量检查
- 集成版本控制
- 支持项目模板生成
- 添加代码执行和测试功能
