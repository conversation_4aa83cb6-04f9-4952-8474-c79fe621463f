#!/usr/bin/env python3
"""
简单的MCP文件服务器
提供文件读写功能
"""

import asyncio
import json
import sys
import os
from typing import Any, Dict, List
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# 创建MCP服务器实例
server = Server("file-server")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="write_file",
            description="写入文件内容，支持代码文件保存",
            inputSchema={
                "type": "object",
                "properties": {
                    "filename": {
                        "type": "string",
                        "description": "要写入的文件名（包含路径）"
                    },
                    "content": {
                        "type": "string",
                        "description": "要写入的文件内容"
                    },
                    "description": {
                        "type": "string",
                        "description": "文件描述（可选）"
                    }
                },
                "required": ["filename", "content"]
            }
        ),
        Tool(
            name="read_file",
            description="读取文件内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "filename": {
                        "type": "string",
                        "description": "要读取的文件名"
                    }
                },
                "required": ["filename"]
            }
        ),
        Tool(
            name="list_files",
            description="列出目录中的文件",
            inputSchema={
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "要列出的目录路径",
                        "default": "."
                    }
                }
            }
        ),
        Tool(
            name="delete_file",
            description="删除文件",
            inputSchema={
                "type": "object",
                "properties": {
                    "filename": {
                        "type": "string",
                        "description": "要删除的文件名"
                    }
                },
                "required": ["filename"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """处理工具调用"""
    
    if name == "write_file":
        filename = arguments["filename"]
        content = arguments["content"]
        description = arguments.get("description", "")

        try:
            # 确保目录存在
            dir_path = os.path.dirname(filename)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)

            abs_path = os.path.abspath(filename)
            file_size = len(content)

            # 分析文件类型
            _, ext = os.path.splitext(filename)
            file_type = {
                '.py': 'Python',
                '.js': 'JavaScript',
                '.html': 'HTML',
                '.css': 'CSS',
                '.java': 'Java',
                '.cpp': 'C++',
                '.c': 'C'
            }.get(ext.lower(), '文本')

            result_text = f"✅ {file_type}文件保存成功!\n"
            result_text += f"📁 路径: {abs_path}\n"
            result_text += f"📄 大小: {file_size} 字符\n"
            if description:
                result_text += f"📝 描述: {description}\n"

            # 显示代码预览（前3行）
            lines = content.split('\n')
            if len(lines) > 0:
                result_text += f"📋 代码预览:\n"
                for i, line in enumerate(lines[:3]):
                    result_text += f"  {i+1}: {line}\n"
                if len(lines) > 3:
                    result_text += f"  ... (共{len(lines)}行)"

            return [TextContent(
                type="text",
                text=result_text
            )]
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"❌ 写入文件失败: {str(e)}"
            )]
    
    elif name == "read_file":
        filename = arguments["filename"]
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            return [TextContent(
                type="text",
                text=f"文件内容:\n{content}"
            )]
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"读取文件失败: {str(e)}"
            )]
    
    elif name == "list_files":
        directory = arguments.get("directory", ".")
        
        try:
            files = os.listdir(directory)
            file_list = "\n".join(files)
            return [TextContent(
                type="text",
                text=f"目录 {directory} 中的文件:\n{file_list}"
            )]
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"列出文件失败: {str(e)}"
            )]
    
    elif name == "delete_file":
        filename = arguments["filename"]
        
        try:
            os.remove(filename)
            return [TextContent(
                type="text",
                text=f"文件 {filename} 删除成功"
            )]
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"删除文件失败: {str(e)}"
            )]
    
    else:
        return [TextContent(
            type="text",
            text=f"未知工具: {name}"
        )]

async def main():
    """启动MCP服务器"""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
