#!/usr/bin/env python3
"""
测试Gemini 2.5 Flash模型的代码生成能力
"""

import os
import asyncio
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.evn')

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from code_generator import CodeGenerator

async def test_gemini_model():
    """测试Gemini模型的代码生成"""
    print("🧪 测试Gemini 2.5 Flash模型")
    print("=" * 50)
    
    generator = CodeGenerator()
    
    # 测试不同类型的代码生成
    test_cases = [
        {
            "request": "生成一个Python函数，计算斐波那契数列的第n项",
            "type": "python"
        },
        {
            "request": "创建一个HTML表单，包含用户注册的基本字段",
            "type": "html"
        },
        {
            "request": "写一个CSS样式，实现一个漂亮的卡片组件",
            "type": "css"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}: {test_case['request']}")
        print("-" * 40)
        
        try:
            # 直接调用LLM生成代码
            code_content = await generator.call_llm_for_code_generation(
                test_case['request'], 
                test_case['type']
            )
            
            if code_content:
                print(f"✅ 代码生成成功!")
                print(f"📄 代码长度: {len(code_content)} 字符")
                
                # 显示代码预览
                preview = code_content[:300] + "..." if len(code_content) > 300 else code_content
                print(f"\n📝 代码预览:")
                print("```")
                print(preview)
                print("```")
                
                # 保存文件
                import time
                timestamp = int(time.time())
                extension = generator.get_file_extension(test_case['type'])
                filename = f"gemini_test_{test_case['type']}_{timestamp}{extension}"
                
                success = generator.save_code_locally(filename, code_content)
                if success:
                    print(f"✅ 已保存到: {filename}")
                else:
                    print("❌ 保存失败")
            else:
                print("❌ 代码生成失败")
                
        except Exception as e:
            print(f"❌ 测试出错: {e}")
        
        print("-" * 40)
    
    print(f"\n📁 所有文件保存在: {os.path.abspath(generator.output_dir)}")

async def test_complex_request():
    """测试复杂的代码生成请求"""
    print("\n🧪 测试复杂代码生成")
    print("=" * 50)
    
    generator = CodeGenerator()
    
    complex_request = """
    生成一个Python类，实现一个简单的任务管理系统，包含以下功能：
    1. 添加任务
    2. 删除任务
    3. 标记任务完成
    4. 列出所有任务
    5. 保存任务到文件
    """
    
    print(f"📝 复杂请求: {complex_request.strip()}")
    print("\n🔄 正在生成代码...")
    
    try:
        code_content = await generator.call_llm_for_code_generation(complex_request, "python")
        
        if code_content:
            print("✅ 复杂代码生成成功!")
            print(f"📄 代码长度: {len(code_content)} 字符")
            
            # 保存文件
            import time
            timestamp = int(time.time())
            filename = f"task_manager_{timestamp}.py"
            
            success = generator.save_code_locally(filename, code_content)
            if success:
                print(f"✅ 已保存到: {filename}")
                
                # 显示代码结构
                lines = code_content.split('\n')
                print(f"\n📊 代码统计:")
                print(f"  - 总行数: {len(lines)}")
                print(f"  - 类定义: {sum(1 for line in lines if 'class ' in line)}")
                print(f"  - 函数定义: {sum(1 for line in lines if 'def ' in line)}")
                
                # 显示前几行
                print(f"\n📝 代码开头:")
                for i, line in enumerate(lines[:10]):
                    print(f"  {i+1:2d}: {line}")
                if len(lines) > 10:
                    print("  ...")
            else:
                print("❌ 保存失败")
        else:
            print("❌ 复杂代码生成失败")
            
    except Exception as e:
        print(f"❌ 复杂测试出错: {e}")

if __name__ == "__main__":
    print("🚀 开始测试Gemini 2.5 Flash模型...")
    
    # 测试基本功能
    asyncio.run(test_gemini_model())
    
    # 测试复杂请求
    asyncio.run(test_complex_request())
    
    print("\n🎉 Gemini模型测试完成!")
    print("💡 如果测试成功，说明模型切换正常工作")
