#!/usr/bin/env python3
"""
测试网络连接和API访问
"""

import requests
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.evn')

def test_network():
    """测试网络连接"""
    print("🌐 测试网络连接")
    print("=" * 40)
    
    # 测试基本网络连接
    try:
        response = requests.get("https://www.google.com", timeout=10)
        print(f"✅ 基本网络连接正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 基本网络连接失败: {e}")
        return False
    
    # 测试OpenRouter API连接
    api_url = os.getenv('OPENROUTER_API_URL', 'https://api.openrouter.ai/api/v1')
    api_key = os.getenv('OPENROUTER_API_KEY')
    
    if not api_key:
        print("❌ 未找到API密钥")
        return False
    
    print(f"🔑 API密钥: {api_key[:20]}...")
    print(f"🌐 API地址: {api_url}")
    
    # 测试API连接
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 测试模型列表接口
        models_url = f"{api_url}/models"
        print(f"🔍 测试模型列表接口: {models_url}")
        
        response = requests.get(models_url, headers=headers, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ OpenRouter API连接成功")
            data = response.json()
            if 'data' in data:
                print(f"📋 可用模型数量: {len(data['data'])}")
                # 检查我们要使用的模型是否可用
                model_name = os.getenv('OpenRouter_models', 'google/gemini-2.5-pro')
                available_models = [model['id'] for model in data['data']]
                if model_name in available_models:
                    print(f"✅ 目标模型 {model_name} 可用")
                else:
                    print(f"⚠️  目标模型 {model_name} 不在可用列表中")
                    print("前5个可用模型:")
                    for model in data['data'][:5]:
                        print(f"  - {model['id']}")
            return True
        else:
            print(f"❌ OpenRouter API连接失败")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def test_simple_api_call():
    """测试简单的API调用"""
    print("\n🧪 测试简单的API调用")
    print("=" * 40)
    
    api_url = os.getenv('OPENROUTER_API_URL', 'https://api.openrouter.ai/api/v1')
    api_key = os.getenv('OPENROUTER_API_KEY')
    model = os.getenv('OpenRouter_models', 'google/gemini-2.5-pro')
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "请回复'测试成功'"}
        ],
        "max_tokens": 50
    }
    
    try:
        chat_url = f"{api_url}/chat/completions"
        print(f"🔄 发送请求到: {chat_url}")
        print(f"🤖 使用模型: {model}")
        
        response = requests.post(chat_url, headers=headers, json=data, timeout=60)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ API调用成功!")
                print(f"🤖 模型回复: {content}")
                return True
            else:
                print("❌ 响应格式异常")
                print(f"响应内容: {result}")
        else:
            print(f"❌ API调用失败")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ API调用出错: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 开始网络和API测试...")
    
    # 测试网络连接
    network_ok = test_network()
    
    if network_ok:
        # 测试API调用
        api_ok = test_simple_api_call()
        
        if api_ok:
            print("\n🎉 所有测试通过! LLM调用应该可以正常工作")
        else:
            print("\n⚠️  API调用失败，请检查API密钥和配置")
    else:
        print("\n❌ 网络连接失败，请检查网络设置")
    
    print("\n💡 如果测试通过，可以运行: python code_generator.py -i")
