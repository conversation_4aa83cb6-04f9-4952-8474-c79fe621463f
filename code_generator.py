#!/usr/bin/env python3
"""
大语言模型调用MCP服务器生成代码文件的示例程序
支持生成Python和HTML代码文件
"""

import os
import sys
import json
import asyncio
import argparse
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import openai
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 加载环境变量
load_dotenv('.evn')

class CodeGenerator:
    def __init__(self):
        self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        self.openrouter_api_url = os.getenv('OPENROUTER_API_URL')
        self.model = os.getenv('OpenRouter_models', 'google/gemini-2.5-pro')

        # 创建输出目录
        self.output_dir = "generated_code"
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"输出目录: {os.path.abspath(self.output_dir)}")

        # 配置OpenAI客户端使用OpenRouter
        # 检查是否需要代理设置
        import httpx

        # 创建HTTP客户端，支持代理
        http_client = None
        proxy = os.getenv('HTTP_PROXY') or os.getenv('HTTPS_PROXY')
        if proxy:
            print(f"🌐 使用代理: {proxy}")
            http_client = httpx.Client(proxies=proxy)

        self.client = openai.OpenAI(
            api_key=self.openrouter_api_key,
            base_url=self.openrouter_api_url,
            http_client=http_client,
            timeout=60.0  # 增加超时时间
        )

        # MCP服务器配置
        self.mcp_command = os.getenv('MCP_SERVER_COMMAND', 'python')
        self.mcp_args = os.getenv('MCP_SERVER_ARGS', 'file_server.py').split()
        self.mcp_session = None
        self.mcp_context = None
        self.read_stream = None
        self.write_stream = None
        
    def is_code_generation_request(self, user_input: str) -> bool:
        """判断用户输入是否为代码生成请求"""
        code_keywords = [
            '生成', '创建', '写', '编写', '制作', 'generate', 'create', 'write', 'make',
            '代码', 'code', '程序', 'program', '脚本', 'script',
            'python', 'html', 'css', 'javascript', 'js'
        ]
        
        user_input_lower = user_input.lower()
        return any(keyword in user_input_lower for keyword in code_keywords)
    
    def detect_code_type(self, user_input: str) -> str:
        """检测要生成的代码类型"""
        user_input_lower = user_input.lower()
        
        if any(keyword in user_input_lower for keyword in ['python', 'py', '.py']):
            return 'python'
        elif any(keyword in user_input_lower for keyword in ['html', 'web', '网页', '页面', '.html']):
            return 'html'
        elif any(keyword in user_input_lower for keyword in ['css', '样式', '.css']):
            return 'css'
        elif any(keyword in user_input_lower for keyword in ['javascript', 'js', '.js']):
            return 'javascript'
        else:
            # 默认返回python
            return 'python'
    
    async def call_llm_for_code_generation(self, user_request: str, code_type: str) -> str:
        """调用大语言模型生成代码"""
        system_prompt = f"""
你是一个专业的代码生成助手。用户请求生成{code_type}代码。
请根据用户的需求生成高质量、可运行的{code_type}代码。
只返回代码内容，不要包含额外的解释或markdown格式。
确保代码符合最佳实践和编码规范。
"""
        
        try:
            print(f"🔄 正在调用模型: {self.model}")
            print(f"🌐 API地址: {self.openrouter_api_url}")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_request}
                ],
                temperature=0.7,
                max_tokens=2000,
                timeout=30  # 添加超时设置
            )

            print("✅ API调用成功")
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"❌ 调用大语言模型时出错: {e}")
            print(f"错误类型: {type(e).__name__}")
            if hasattr(e, 'response'):
                print(f"HTTP状态码: {e.response.status_code if e.response else 'N/A'}")
            return None
    
    async def setup_mcp_client(self):
        """设置MCP客户端连接"""
        try:
            server_params = StdioServerParameters(
                command=self.mcp_command,
                args=self.mcp_args
            )

            # 使用异步上下文管理器连接MCP服务器
            self.mcp_context = stdio_client(server_params)
            self.read_stream, self.write_stream = await self.mcp_context.__aenter__()

            # 创建客户端会话
            self.mcp_session = ClientSession(self.read_stream, self.write_stream)
            await self.mcp_session.initialize()
            print("✅ MCP服务器连接成功")
            return True
        except Exception as e:
            print(f"⚠️  连接MCP服务器失败: {e}")
            print("将使用本地文件保存功能")
            self.mcp_session = None
            self.mcp_context = None
            return False

    async def cleanup_mcp_client(self):
        """清理MCP客户端连接"""
        try:
            if self.mcp_context:
                await self.mcp_context.__aexit__(None, None, None)
                print("✅ MCP连接已关闭")
        except Exception as e:
            print(f"⚠️  关闭MCP连接时出错: {e}")
        finally:
            self.mcp_session = None
            self.mcp_context = None
            self.read_stream = None
            self.write_stream = None
    
    async def save_code_via_mcp(self, filename: str, code_content: str) -> bool:
        """通过MCP服务器保存代码文件"""
        if not self.mcp_session:
            print("MCP服务器未连接，使用本地保存")
            return self.save_code_locally(filename, code_content)

        try:
            # 在输出目录中保存文件
            filepath = os.path.join(self.output_dir, filename)
            result = await self.mcp_session.call_tool(
                "write_file",
                {
                    "filename": filepath,
                    "content": code_content
                }
            )
            print(f"✅ 通过MCP保存文件成功: {os.path.abspath(filepath)}")
            return True
        except Exception as e:
            print(f"⚠️  通过MCP保存文件失败: {e}")
            print("回退到本地保存")
            # 如果MCP保存失败，直接保存到本地
            return self.save_code_locally(filename, code_content)
    
    def save_code_locally(self, filename: str, code_content: str) -> bool:
        """本地保存代码文件"""
        try:
            # 确保文件保存在输出目录中
            filepath = os.path.join(self.output_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(code_content)
            print(f"代码已保存到: {os.path.abspath(filepath)}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def get_file_extension(self, code_type: str) -> str:
        """根据代码类型获取文件扩展名"""
        extensions = {
            'python': '.py',
            'html': '.html',
            'css': '.css',
            'javascript': '.js'
        }
        return extensions.get(code_type, '.txt')
    
    async def process_request(self, user_input: str, auto_save: bool = False):
        """处理用户请求"""
        # 判断是否为代码生成请求
        if not self.is_code_generation_request(user_input):
            print("这不是一个代码生成请求。请输入包含'生成'、'创建'、'代码'等关键词的请求。")
            return

        # 检测代码类型
        code_type = self.detect_code_type(user_input)
        print(f"检测到代码类型: {code_type}")

        # 调用大语言模型生成代码
        print("正在生成代码...")
        code_content = await self.call_llm_for_code_generation(user_input, code_type)

        if not code_content:
            print("代码生成失败")
            return

        # 生成文件名
        import time
        timestamp = int(time.time())
        extension = self.get_file_extension(code_type)
        filename = f"generated_code_{timestamp}{extension}"

        # 显示生成的代码
        print(f"\n生成的{code_type}代码:")
        print("-" * 50)
        print(code_content)
        print("-" * 50)

        # 保存文件
        output_path = os.path.join(self.output_dir, filename)

        if auto_save:
            # 自动保存模式
            success = await self.save_code_via_mcp(filename, code_content)
            if success:
                print(f"✅ 文件已自动保存!")
                print(f"📁 文件位置: {os.path.abspath(output_path)}")
                print(f"📄 文件大小: {len(code_content)} 字符")
            else:
                print("❌ 文件保存失败")
        else:
            # 交互式保存模式
            save_choice = input(f"\n是否保存到文件 {output_path}? (y/n): ").lower()
            if save_choice in ['y', 'yes', '是']:
                # 尝试通过MCP保存，如果失败则本地保存
                success = await self.save_code_via_mcp(filename, code_content)

                if success:
                    print(f"✅ 文件保存成功!")
                    # 显示文件内容预览
                    print(f"📁 文件位置: {os.path.abspath(output_path)}")
                    print(f"📄 文件大小: {len(code_content)} 字符")
                else:
                    print("❌ 文件保存失败")
            else:
                print("代码未保存")

async def main():
    parser = argparse.ArgumentParser(description='大语言模型代码生成器')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='交互模式')
    parser.add_argument('--auto-save', '-a', action='store_true',
                       help='自动保存生成的代码，不询问用户')
    parser.add_argument('request', nargs='?',
                       help='代码生成请求')

    args = parser.parse_args()
    
    generator = CodeGenerator()

    try:
        # 尝试连接MCP服务器（可选）
        await generator.setup_mcp_client()

        if args.interactive:
            print("进入交互模式。输入 'quit' 或 'exit' 退出。")
            while True:
                try:
                    user_input = input("\n请输入代码生成请求: ").strip()
                    if user_input.lower() in ['quit', 'exit', '退出']:
                        break
                    if user_input:
                        await generator.process_request(user_input, auto_save=args.auto_save)
                except KeyboardInterrupt:
                    print("\n程序已退出")
                    break
        elif args.request:
            await generator.process_request(args.request, auto_save=args.auto_save)
        else:
            print("请提供代码生成请求或使用 -i 进入交互模式")
            print("示例: python code_generator.py '生成一个Python计算器程序'")

    finally:
        # 清理MCP连接
        await generator.cleanup_mcp_client()

if __name__ == "__main__":
    asyncio.run(main())
