#!/usr/bin/env python3
"""
启动脚本 - 自动启动MCP服务器和代码生成器
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path

def start_mcp_server():
    """在后台启动MCP文件服务器"""
    try:
        print("正在启动MCP文件服务器...")
        process = subprocess.Popen(
            [sys.executable, "file_server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        time.sleep(2)  # 等待服务器启动
        
        if process.poll() is None:
            print("✅ MCP文件服务器启动成功")
            return process
        else:
            print("❌ MCP文件服务器启动失败")
            return None
    except Exception as e:
        print(f"❌ 启动MCP服务器时出错: {e}")
        return None

def check_dependencies():
    """检查依赖是否安装"""
    # 包名映射：pip包名 -> 导入名
    package_mapping = {
        'openai': 'openai',
        'python-dotenv': 'dotenv',
        'mcp': 'mcp'
    }
    missing_packages = []

    for pip_name, import_name in package_mapping.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(pip_name)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_config():
    """检查配置文件"""
    config_file = Path(".evn")
    if not config_file.exists():
        print("❌ 配置文件 .evn 不存在")
        return False
    
    # 检查API密钥
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
        if 'OPENROUTER_API_KEY=' not in content or 'sk-or-v1-' not in content:
            print("⚠️  请确保在 .evn 文件中设置了有效的 OPENROUTER_API_KEY")
    
    print("✅ 配置文件检查完成")
    return True

def main():
    """主函数"""
    print("🚀 代码生成器启动程序")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查配置
    if not check_config():
        return
    
    # 启动MCP服务器
    mcp_process = start_mcp_server()
    
    try:
        # 启动代码生成器
        print("\n🤖 启动代码生成器...")
        print("进入交互模式，输入 'quit' 退出")
        print("-" * 40)
        
        # 运行代码生成器
        subprocess.run([sys.executable, "code_generator.py", "-i"])
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    
    finally:
        # 清理MCP服务器进程
        if mcp_process and mcp_process.poll() is None:
            print("\n🛑 正在关闭MCP服务器...")
            mcp_process.terminate()
            mcp_process.wait()
            print("✅ MCP服务器已关闭")

if __name__ == "__main__":
    main()
