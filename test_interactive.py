#!/usr/bin/env python3
"""
测试交互式代码生成（自动化测试）
"""

import os
import asyncio
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.evn')

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from code_generator import CodeGenerator

async def test_single_request():
    """测试单个代码生成请求"""
    print("🧪 测试单个代码生成请求")
    print("=" * 50)
    
    generator = CodeGenerator()
    
    # 测试请求
    request = "生成一个Python函数，用于验证邮箱地址格式是否正确"
    
    print(f"📝 请求: {request}")
    
    try:
        # 使用自动保存模式
        await generator.process_request(request, auto_save=True)
        print("✅ 请求处理完成")
    except Exception as e:
        print(f"❌ 请求处理失败: {e}")

async def test_multiple_requests():
    """测试多个代码生成请求"""
    print("\n🧪 测试多个代码生成请求")
    print("=" * 50)
    
    generator = CodeGenerator()
    
    requests = [
        "生成一个JavaScript函数，实现数组去重",
        "创建一个CSS动画，实现按钮悬停效果",
        "写一个Python装饰器，用于计算函数执行时间"
    ]
    
    for i, request in enumerate(requests, 1):
        print(f"\n🔍 请求 {i}: {request}")
        print("-" * 30)
        
        try:
            await generator.process_request(request, auto_save=True)
            print(f"✅ 请求 {i} 完成")
        except Exception as e:
            print(f"❌ 请求 {i} 失败: {e}")
        
        print("-" * 30)

async def test_mcp_integration():
    """测试MCP集成"""
    print("\n🧪 测试MCP集成")
    print("=" * 50)
    
    generator = CodeGenerator()
    
    # 尝试连接MCP服务器
    mcp_connected = await generator.setup_mcp_client()
    
    if mcp_connected:
        print("✅ MCP服务器连接成功")
        
        # 测试通过MCP保存文件
        test_code = """# MCP测试代码
def hello_mcp():
    return "Hello from MCP!"

if __name__ == "__main__":
    print(hello_mcp())
"""
        
        try:
            success = await generator.save_code_via_mcp("mcp_test.py", test_code)
            if success:
                print("✅ MCP文件保存测试成功")
            else:
                print("❌ MCP文件保存测试失败")
        except Exception as e:
            print(f"❌ MCP测试出错: {e}")
        
        # 清理连接
        await generator.cleanup_mcp_client()
    else:
        print("⚠️  MCP服务器连接失败，跳过MCP测试")

def show_results():
    """显示生成结果"""
    print("\n📊 生成结果统计")
    print("=" * 50)
    
    output_dir = "generated_code"
    if os.path.exists(output_dir):
        files = [f for f in os.listdir(output_dir) if f.startswith(('gemini_', 'task_', 'generated_code_'))]
        
        print(f"📁 输出目录: {os.path.abspath(output_dir)}")
        print(f"📄 生成文件数量: {len(files)}")
        
        # 按类型分组
        file_types = {}
        for file in files:
            ext = os.path.splitext(file)[1]
            file_types[ext] = file_types.get(ext, 0) + 1
        
        print("\n📋 文件类型统计:")
        for ext, count in file_types.items():
            print(f"  {ext}: {count} 个文件")
        
        print("\n📝 最新生成的文件:")
        files.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x)), reverse=True)
        for file in files[:5]:
            filepath = os.path.join(output_dir, file)
            size = os.path.getsize(filepath)
            print(f"  📄 {file} ({size} 字节)")
    else:
        print("❌ 输出目录不存在")

if __name__ == "__main__":
    print("🚀 开始交互式测试...")
    
    # 测试单个请求
    asyncio.run(test_single_request())
    
    # 测试多个请求
    asyncio.run(test_multiple_requests())
    
    # 测试MCP集成
    asyncio.run(test_mcp_integration())
    
    # 显示结果
    show_results()
    
    print("\n🎉 交互式测试完成!")
    print("\n💡 现在您可以使用以下命令:")
    print("  python code_generator.py -i                    # 交互模式")
    print("  python code_generator.py -a '生成代码请求'      # 自动保存模式")
    print("  python start.py                               # 一键启动")
