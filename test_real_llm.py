#!/usr/bin/env python3
"""
测试真实的LLM调用
"""

import os
import asyncio
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.evn')

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from code_generator import CodeGenerator

async def test_real_llm():
    """测试真实的LLM调用"""
    print("🧪 测试真实的LLM调用")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("❌ 未找到OPENROUTER_API_KEY")
        return
    
    print(f"✅ API密钥已配置: {api_key[:20]}...")
    
    generator = CodeGenerator()
    
    # 测试简单的代码生成请求
    test_request = "生成一个简单的Python函数，计算两个数的和"
    
    print(f"\n📝 测试请求: {test_request}")
    print("🔄 正在调用OpenRouter API...")
    
    try:
        # 直接调用LLM
        code_content = await generator.call_llm_for_code_generation(test_request, "python")
        
        if code_content:
            print("✅ LLM调用成功!")
            print(f"📄 生成的代码长度: {len(code_content)} 字符")
            print("\n生成的代码:")
            print("-" * 40)
            print(code_content)
            print("-" * 40)
            
            # 保存到文件
            success = generator.save_code_locally("test_real_llm_output.py", code_content)
            if success:
                print("✅ 代码已保存到文件")
            else:
                print("❌ 文件保存失败")
        else:
            print("❌ LLM调用失败，返回空内容")
    
    except Exception as e:
        print(f"❌ LLM调用出错: {e}")
        print("请检查:")
        print("1. API密钥是否有效")
        print("2. 网络连接是否正常")
        print("3. OpenRouter账户是否有余额")

async def test_full_process():
    """测试完整的代码生成流程"""
    print("\n" + "=" * 50)
    print("🧪 测试完整的代码生成流程")
    print("=" * 50)
    
    generator = CodeGenerator()
    
    # 测试请求
    test_requests = [
        "生成一个Python函数，用于检查一个数是否为质数",
        "创建一个简单的HTML按钮"
    ]
    
    for i, request in enumerate(test_requests, 1):
        print(f"\n🔍 测试 {i}: {request}")
        print("-" * 30)
        
        try:
            await generator.process_request(request)
            print("✅ 流程完成")
        except Exception as e:
            print(f"❌ 流程出错: {e}")
        
        print("-" * 30)

if __name__ == "__main__":
    print("🚀 开始测试真实的LLM调用...")
    
    # 首先测试直接的LLM调用
    asyncio.run(test_real_llm())
    
    # 然后测试完整流程
    asyncio.run(test_full_process())
    
    print("\n🎉 测试完成!")
    print("\n💡 如果测试成功，说明LLM调用正常工作")
    print("💡 如果测试失败，请检查API密钥和网络连接")
