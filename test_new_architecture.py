#!/usr/bin/env python3
"""
测试新架构：AI聊天 → tool_use → MCP调用
"""

import os
import asyncio
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.evn')

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_chat_with_mcp import AIChat

async def test_architecture():
    """测试新架构"""
    print("🧪 测试新架构：AI聊天 → tool_use → MCP调用")
    print("=" * 60)
    
    chat = AIChat()
    
    # 连接MCP服务器
    mcp_connected = await chat.setup_mcp_client()
    if mcp_connected:
        print("✅ MCP服务器连接成功")
    else:
        print("⚠️  MCP服务器连接失败，将使用本地保存")
    
    # 测试用例
    test_cases = [
        "请生成一个Python函数，用于计算两个数的最大公约数",
        "创建一个JavaScript函数，实现深拷贝对象",
        "写一个CSS样式，实现一个漂亮的加载动画",
        "生成一个HTML页面，展示个人简历"
    ]
    
    print(f"\n📋 测试用例:")
    for i, case in enumerate(test_cases, 1):
        print(f"  {i}. {case}")
    
    print("\n" + "=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}: {test_case}")
        print("-" * 40)
        
        try:
            # 发送请求给AI
            response = await chat.chat_with_ai(test_case)
            
            # 显示响应（过滤tool_use）
            chat.display_response(response)
            
            print("✅ 测试完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("-" * 40)
    
    # 清理连接
    await chat.cleanup_mcp_client()
    
    # 检查生成的文件
    print(f"\n📁 检查生成的文件:")
    output_dir = chat.output_dir
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        new_files = [f for f in files if any(f.startswith(prefix) for prefix in ['generated_code_', 'function_', 'style_', 'page_'])]
        
        print(f"📄 新生成文件数量: {len(new_files)}")
        for file in new_files:
            filepath = os.path.join(output_dir, file)
            size = os.path.getsize(filepath)
            print(f"  📄 {file} ({size} 字节)")
    else:
        print("❌ 输出目录不存在")

async def test_single_request():
    """测试单个请求"""
    print("\n🧪 测试单个复杂请求")
    print("=" * 60)
    
    chat = AIChat()
    await chat.setup_mcp_client()
    
    complex_request = """
    请生成一个完整的Python类，实现一个简单的图书管理系统，包含以下功能：
    1. 添加图书
    2. 删除图书  
    3. 查找图书
    4. 列出所有图书
    5. 保存到JSON文件
    请包含完整的错误处理和文档注释。
    """
    
    print(f"📝 复杂请求: {complex_request.strip()}")
    
    try:
        response = await chat.chat_with_ai(complex_request)
        chat.display_response(response)
        print("✅ 复杂请求测试完成")
    except Exception as e:
        print(f"❌ 复杂请求测试失败: {e}")
    
    await chat.cleanup_mcp_client()

async def test_conversation():
    """测试对话连续性"""
    print("\n🧪 测试对话连续性")
    print("=" * 60)
    
    chat = AIChat()
    await chat.setup_mcp_client()
    
    # 连续对话
    conversations = [
        "请生成一个Python函数，计算斐波那契数列",
        "现在请优化这个函数，使用记忆化提高性能",
        "再生成一个测试文件，测试这个优化后的函数"
    ]
    
    for i, msg in enumerate(conversations, 1):
        print(f"\n💬 对话 {i}: {msg}")
        print("-" * 30)
        
        try:
            response = await chat.chat_with_ai(msg)
            chat.display_response(response)
        except Exception as e:
            print(f"❌ 对话失败: {e}")
        
        print("-" * 30)
    
    await chat.cleanup_mcp_client()

if __name__ == "__main__":
    print("🚀 开始测试新架构...")
    
    # 测试基本架构
    asyncio.run(test_architecture())
    
    # 测试复杂请求
    asyncio.run(test_single_request())
    
    # 测试对话连续性
    asyncio.run(test_conversation())
    
    print("\n🎉 所有测试完成!")
    print("\n💡 现在可以使用:")
    print("  python ai_chat_with_mcp.py -i    # 交互模式")
    print("  python ai_chat_with_mcp.py '生成代码请求'  # 单次请求")
