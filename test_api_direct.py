#!/usr/bin/env python3
"""
直接测试API调用，不依赖复杂的库
"""

import json
import urllib.request
import urllib.parse
import urllib.error
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.evn')

def test_api_with_urllib():
    """使用urllib直接测试API"""
    print("🧪 使用urllib测试OpenRouter API")
    print("=" * 50)
    
    api_key = os.getenv('OPENROUTER_API_KEY')
    api_url = os.getenv('OPENROUTER_API_URL', 'https://api.openrouter.ai/api/v1')
    model = os.getenv('OpenRouter_models', 'google/gemini-2.5-pro')
    
    if not api_key:
        print("❌ 未找到API密钥")
        return False
    
    print(f"🔑 API密钥: {api_key[:20]}...")
    print(f"🌐 API地址: {api_url}")
    print(f"🤖 模型: {model}")
    
    # 准备请求数据
    data = {
        "model": model,
        "messages": [
            {
                "role": "system", 
                "content": "你是一个代码生成助手。请生成简洁的代码，不要包含markdown格式。"
            },
            {
                "role": "user", 
                "content": "生成一个Python函数，计算两个数的和"
            }
        ],
        "max_tokens": 500,
        "temperature": 0.7
    }
    
    # 转换为JSON
    json_data = json.dumps(data).encode('utf-8')
    
    # 创建请求
    url = f"{api_url}/chat/completions"
    req = urllib.request.Request(url)
    req.add_header('Content-Type', 'application/json')
    req.add_header('Authorization', f'Bearer {api_key}')
    req.add_header('User-Agent', 'Python-urllib/3.0')
    
    try:
        print("🔄 发送API请求...")
        with urllib.request.urlopen(req, json_data, timeout=60) as response:
            print(f"✅ 请求成功! 状态码: {response.status}")
            
            # 读取响应
            response_data = response.read().decode('utf-8')
            result = json.loads(response_data)
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print("🎉 API调用成功!")
                print(f"📝 生成的代码:")
                print("-" * 40)
                print(content)
                print("-" * 40)
                
                # 保存到文件
                output_dir = "generated_code"
                os.makedirs(output_dir, exist_ok=True)
                
                filename = os.path.join(output_dir, "test_api_output.py")
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 代码已保存到: {filename}")
                return True
            else:
                print("❌ 响应格式异常")
                print(f"响应内容: {result}")
                return False
                
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误: {e.code} {e.reason}")
        try:
            error_content = e.read().decode('utf-8')
            print(f"错误详情: {error_content}")
        except:
            pass
        return False
        
    except urllib.error.URLError as e:
        print(f"❌ URL错误: {e.reason}")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. DNS解析失败")
        print("3. 防火墙阻止")
        print("4. 需要代理设置")
        return False
        
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_with_fallback_dns():
    """尝试使用不同的DNS解析"""
    print("\n🔍 尝试DNS解析测试")
    print("=" * 30)
    
    import socket
    
    hosts_to_test = [
        "api.openrouter.ai",
        "openrouter.ai",
        "google.com"
    ]
    
    for host in hosts_to_test:
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ {host} -> {ip}")
        except socket.gaierror as e:
            print(f"❌ {host} -> DNS解析失败: {e}")

if __name__ == "__main__":
    print("🚀 开始直接API测试...")
    
    # 先测试DNS解析
    test_with_fallback_dns()
    
    # 然后测试API
    success = test_api_with_urllib()
    
    if success:
        print("\n🎉 API测试成功! 真实的LLM调用正常工作")
        print("现在可以使用: python code_generator.py -i")
    else:
        print("\n❌ API测试失败")
        print("请检查:")
        print("1. 网络连接")
        print("2. API密钥是否有效")
        print("3. 是否需要代理设置")
        print("4. 防火墙设置")
